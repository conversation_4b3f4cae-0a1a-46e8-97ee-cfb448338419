package com.crentsil.demo.controller;

import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/students")
public class StudentController {
    private final JpaRepository<Student, Long> studentRepository;

    @Autowired
    public StudentController(JpaRepository<Student, Long> studentRepository) {
        this.studentRepository = studentRepository;
    }

    @PostMapping("create")
    public Student addStudent(@RequestBody Student student) {
        System.out.println("Adding student: " + student);
        return studentRepository.save(student);
    }

@PostMapping("search")
    public Student findStudents(@RequestBody Student student) {
        System.out.println("Adding student: " + student);
        return studentRepository.save(student);
    }


}
