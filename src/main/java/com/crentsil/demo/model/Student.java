package com.crentsil.demo.model;

import jakarta.persistence.*;

import java.time.LocalDate;

@Entity
@Table(name = "student")
public class Student {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = true)
    private String name;

    @Column(nullable = true)
    private Integer age;

    @Column(nullable = true)
    private String dob;// to make sure the format is "yyyy-mm-dd"

    @Column(nullable = true)
    private String password;




}
